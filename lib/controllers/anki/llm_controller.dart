import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:anki_guru/controllers/common.dart';
import 'dart:convert';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import 'package:path/path.dart' as p;

// Define storage keys for LLM card
class LLMCardStorageKeys {
  static const String provider = 'llm_provider';
  static const String model = 'llm_model';
  static const String customModel = 'llm_custom_model';
  static const String systemPrompt = 'llm_system_prompt';
  static const String userPrompt = 'llm_user_prompt';
  static const String temperature = 'llm_temperature';
  static const String maxTokens = 'llm_max_tokens';
  static const String difficulties = 'llm_difficulties';
  static const String promptList =
      'llm_prompt_list'; // Add storage key for prompts
  static const String promptSortOption =
      'llm_prompt_sort_option'; // Sort option storage key
  static const String promptName = 'llm_prompt_name'; // Selected prompt name
  static const String maxConcurrentRequests =
      'llm_max_concurrent_requests'; // 最大并发请求数
  static const String logdir = 'llm_logdir'; // 日志目录
  static const String topP = 'llm_top_p'; // Top P
  static const String topK = 'llm_top_k'; // Top K
  static const String chunkSize = 'llm_chunk_size'; // 分块大小
  static const String timeout = 'llm_timeout'; // 超时
  static const String clozeMode = 'llm_cloze_mode'; // 填空模式
  static const String isPerClozePerCard = 'llm_is_per_cloze_per_card'; // 一空一卡
  static const String isHtmlEscape = 'llm_is_html_escape'; // 是否转义HTML
  static const String useAITags = 'llm_use_ai_tags'; // 是否转义HTML
  // API Keys for different providers
  static String apiKeyForProvider(String provider) => 'llm_api_key_$provider';

  // API Endpoints for different providers
  static String apiEndpointForProvider(String provider) =>
      'llm_api_endpoint_$provider';
}

class LLMCardPageController extends GetxController {
  // UI controllers
  final tabController = ShadTabsController(value: 'card');

  // 基础数据
  final parentDeckList = <String>[
    "anki.llm_card.guru_import".tr,
    "anki.llm_card.system_default".tr,
    "anki.llm_card.test".tr
  ].obs;
  final tagsList = <String>["anki.llm_card.guru_import".tr].obs;
  final promptList = <Map<String, String>>[].obs;

  // 提示词列表
  final promptNameInput = ''.obs;
  final promptObjList = <Map<String, dynamic>>[]
      .obs; // Changed to <String, dynamic> to store dates
  // 提示词排序选项
  final promptSortOption = 'modifyTime'.obs;
  // Model selection
  final modelProviderList = [
    {"label": "anki.llm_card.guru_official".tr, "value": "guru"},
    {"label": "OpenRouter", "value": "openrouter"},
    {"label": "SiliconFlow", "value": "siliconflow"},
    {"label": "CloseAI", "value": "closeai"},
    {"label": "anki.llm_card.custom".tr, "value": "custom"},
  ];

  final modelList = {
    "guru": [
      {"label": "gemini-2.5-flash", "value": "gemini-2.5-flash"},
      {"label": "gemini-2.5-pro", "value": "gemini-2.5-pro"},
      {
        "label": "gemini-2.5-flash-preview-05-20",
        "value": "gemini-2.5-flash-preview-05-20"
      },
      {
        "label": "gemini-2.5-flash-lite-preview-06-17",
        "value": "gemini-2.5-flash-lite-preview-06-17"
      },
    ],
    "openrouter": [
      {
        "label": "Qwen: Qwen2.5 VL 72B Instruct (free)",
        "value": "qwen/qwen2.5-vl-72b-instruct:free"
      },
      {
        "label": "DeepSeek: DeepSeek V3 0324 (free)",
        "value": "deepseek/deepseek-chat-v3-0324:free"
      },
      {"label": "DeepSeek: R1 (free)", "value": "deepseek/deepseek-r1:free"},
    ],
    "closeai": [
      {"label": "deepseek-chat", "value": "deepseek-chat"},
      {"label": "deepseek-reasoner", "value": "deepseek-reasoner"},
      {
        "label": "gemini-2.5-flash-preview-04-17",
        "value": "gemini-2.5-flash-preview-04-17"
      },
      {
        "label": "gemini-2.5-pro-preview-03-25",
        "value": "gemini-2.5-pro-preview-03-25"
      },
      {
        "label": "gemini-2.5-pro-exp-03-25",
        "value": "gemini-2.5-pro-exp-03-25"
      },
      {"label": "gemini-2.0-flash", "value": "gemini-2.0-flash"},
      {"label": "gemini-1.5-flash", "value": "gemini-1.5-flash"},
      {
        "label": "claude-3-7-sonnet-latest",
        "value": "claude-3-7-sonnet-latest"
      },
      {
        "label": "claude-3-5-sonnet-latest",
        "value": "claude-3-5-sonnet-latest"
      },
    ],
    "siliconflow": [
      {
        "label": "Pro/Qwen/Qwen2.5-VL-7B-Instruct",
        "value": "Pro/Qwen/Qwen2.5-VL-7B-Instruct"
      },
      {
        "label": "Qwen/Qwen2.5-VL-32B-Instruct",
        "value": "Qwen/Qwen2.5-VL-32B-Instruct"
      },
      {
        "label": "Qwen/Qwen2.5-VL-72B-Instruct",
        "value": "Qwen/Qwen2.5-VL-72B-Instruct"
      },
      {"label": "Qwen/QVQ-72B-Preview", "value": "Qwen/QVQ-72B-Preview"},
      {
        "label": "deepseek-ai/deepseek-vl2",
        "value": "deepseek-ai/deepseek-vl2"
      },
      {
        "label": "Pro/deepseek-ai/DeepSeek-V3",
        "value": "Pro/deepseek-ai/DeepSeek-V3"
      },
      {
        "label": "Pro/deepseek-ai/DeepSeek-R1",
        "value": "Pro/deepseek-ai/DeepSeek-R1"
      },
      {"label": "Qwen/Qwen3-235B-A22B", "value": "Qwen/Qwen3-235B-A22B"},
      {"label": "Qwen/Qwen3-30B-A3B", "value": "Qwen/Qwen3-30B-A3B"},
      {"label": "THUDM/GLM-4-32B-0414", "value": "THUDM/GLM-4-32B-0414"},
      {"label": "THUDM/GLM-Z1-32B-0414", "value": "THUDM/GLM-Z1-32B-0414"},
    ],
    "openai": [
      {"label": "gpt-4o-2024-08-06", "value": "gpt-4o-2024-08-06"},
      {"label": "gpt-4.1-2025-04-14", "value": "gpt-4.1-2025-04-14"},
      {"label": "o4-mini-2025-04-16", "value": "o4-mini-2025-04-16"},
      {"label": "o3-2025-04-16", "value": "o3-2025-04-16"},
      {"label": "gpt-4o-mini-tts", "value": "gpt-4o-mini-tts"},
    ],
    "custom": [
      {"label": "anki.llm_card.custom_model".tr, "value": "custom"},
    ],
  };

  final protocolList = [
    {"label": "OpenAI", "value": "openai"},
    {"label": "Anthropic", "value": "anthropic"},
    {"label": "Google", "value": "google"},
    {"label": "DeepSeek", "value": "deepseek"},
    {"label": "Ollama", "value": "ollama"},
    {"label": "Groq", "value": "groq"},
    {"label": "AzureOpenAI", "value": "azureopenai"},
    {"label": "Phind", "value": "phind"},
    {"label": "XAI", "value": "xai"},
    {"label": "ElevenLabs", "value": "elevenlabs"},
  ];
  final cardTypeList = [
    {"label": "anki.llm_card.qa_card".tr, "value": "qa"},
    {"label": "anki.llm_card.cloze_card".tr, "value": "cloze"},
    {"label": "anki.llm_card.choice_card".tr, "value": "choice"},
    {"label": "anki.llm_card.multi_choice_card".tr, "value": "multi_choice"},
    {"label": "anki.llm_card.judge_card".tr, "value": "judge"},
  ];
  final clozeModeList = [
    {
      "label": "anki.llm_card.mask_one_guess_one".tr,
      "value": "mask_one_guess_one"
    },
    {
      "label": "anki.llm_card.mask_all_guess_one".tr,
      "value": "mask_all_guess_one"
    },
    {
      "label": "anki.llm_card.mask_all_guess_all".tr,
      "value": "mask_all_guess_all"
    },
    {"label": "anki.llm_card.free_guess".tr, "value": "free_guess"},
  ].obs;
  // 表单参数
  final selectedModelProvider = "guru".obs;
  final selectedModel = "gemini-2.5-flash".obs;
  final customModel = "".obs;
  final selectedProtocol = "openai".obs;
  final apiEndpoint = "https://ai.kevin2li.top/v1/".obs;
  final parentDeck = 'anki.llm_card.guru_import'.tr.obs;
  final promptName = ''.obs;
  final tags = <String>[].obs;
  final difficulties = <String>["none"].obs;
  final temperature = 0.7.obs;
  final maxTokens = 30000.obs;
  final maxConcurrentRequests = 1.obs; // 最大并发请求数
  final isShowSource = true.obs;
  final clozeMode = "free_guess".obs;
  final isPerClozePerCard = false.obs;
  final isAnswerCloze = true.obs;
  final isAutoTag = true.obs;
  final isReasoning = false.obs;
  final isHtmlEscape = false.obs;
  final useAITags = true.obs;
  final selectedFilePaths = <String>[].obs;
  final cardTypes = <String>["qa"].obs;
  final logdir = "".obs;
  final chunkSize = 10000.obs;
  final pageRange = "".obs;
  final topP = 0.95.obs;
  final topK = 10.obs;
  final timeout = 1200.obs;
  final apiKey = "".obs;
  final systemPrompt = "".obs;
  final userPrompt = "".obs;
  final defaultSystemPrompt = PromptGenerator.getDefaultSystemPrompt();

  // Dependencies
  final _storage = StorageManager();
  final messageController = Get.find<MessageController>();
  final progressController = Get.find<ProgressController>();
  final settingController = Get.find<SettingController>();
  final ankiConnectController = Get.find<AnkiConnectController>();
  final difficultyList = [
    {"label": "anki.llm_card.difficulty_none".tr, "value": "none"},
    {"label": "anki.llm_card.difficulty_basic".tr, "value": "basic"},
    {"label": "anki.llm_card.difficulty_medium".tr, "value": "medium"},
    {"label": "anki.llm_card.difficulty_advanced".tr, "value": "advanced"},
  ];

  @override
  void onInit() {
    super.onInit();
    if (ankiConnectController.parentDeckList.isNotEmpty) {
      parentDeck.value = ankiConnectController.parentDeckList[0];
    }
    _loadSavedSettings();
    _loadPromptList(); // Load saved prompts
    _loadPromptSortOption(); // Load prompt sorting preference
    systemPrompt.value = defaultSystemPrompt;

    // 监听提示词列表变化，更新promptList
    ever(promptObjList, (_) {
      _updatePromptList();
    });

    // 监听提示词名称变化，自动保存并刷新提示词列表
    ever(promptName, (value) {
      _storage.write(StorageBox.default_, LLMCardStorageKeys.promptName, value);
      // 当选择的提示词变化时，重新加载提示词列表确保数据一致性
      if (value.isNotEmpty) {
        _loadPromptList();
        logger.i('anki.llm_card.prompt_selection_changed'
            .trParams({'value': value}));
      }
    });

    // 监听模型提供商变化，加载对应的API密钥和端点
    ever(selectedModelProvider, (value) {
      _loadProviderSettings(value);
    });

    // 监听排序选项变化，自动保存
    ever(promptSortOption, (_) {
      _savePromptSortOption();
    });

    // 监听API密钥和端点变化，自动保存
    ever(apiKey, (_) {
      saveSettings(showToast: false);
    });
    ever(apiEndpoint, (_) {
      saveSettings(showToast: false);
    });
    ever(topP, (_) {
      saveSettings(showToast: false);
    });
    ever(topK, (_) {
      saveSettings(showToast: false);
    });
    ever(timeout, (_) {
      saveSettings(showToast: false);
    });
    ever(chunkSize, (_) {
      saveSettings(showToast: false);
    });
    ever(maxTokens, (_) {
      saveSettings(showToast: false);
    });
    ever(clozeMode, (_) {
      saveSettings(showToast: false);
    });
    ever(isPerClozePerCard, (_) {
      saveSettings(showToast: false);
    });
    ever(isHtmlEscape, (_) {
      saveSettings(showToast: false);
    });
    ever(useAITags, (_) {
      saveSettings(showToast: false);
    });
    ever(customModel, (_) {
      saveSettings(showToast: false);
    });
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 更新promptList以便在LLMCard中使用
  void _updatePromptList() {
    // 清空现有列表
    promptList.clear();

    // 将promptObjList中的提示词添加到promptList
    for (final prompt in promptObjList) {
      promptList.add({
        'label': prompt['name'] as String,
        'value': prompt['name'] as String,
      });
    }

    // 当列表有内容且当前未选择任何提示词时，自动选择第一个
    if (promptName.value.isEmpty && promptList.isNotEmpty) {
      promptName.value = promptList.first['value'] as String;
    }
  }

  // Load saved prompts from storage
  void _loadPromptList() {
    final savedPrompts = _storage.read<List<dynamic>>(
      StorageBox.default_,
      LLMCardStorageKeys.promptList,
      [],
    );

    if (savedPrompts.isNotEmpty) {
      promptObjList.value = savedPrompts
          .map((item) => Map<String, dynamic>.from(item as Map))
          .toList();
    }

    // 确保在加载后立即更新promptList，即使promptObjList没有变化
    _updatePromptList();
  }

  // Save prompts to storage
  void _savePromptList() {
    _storage.write(
      StorageBox.default_,
      LLMCardStorageKeys.promptList,
      promptObjList,
    );
  }

  // Add a new prompt
  void addPrompt(String name, String content) {
    final now = DateTime.now().toIso8601String();

    // 检查是否存在重名提示词，如果存在则添加序号后缀
    String uniqueName = name;
    int suffix = 1;

    while (_isPromptNameExists(uniqueName)) {
      uniqueName = '$name ($suffix)';
      suffix++;
    }

    if (uniqueName != name) {
      logger.i('anki.llm_card.prompt_name_exists_renamed'
          .trParams({'name': name, 'uniqueName': uniqueName}));
    }

    // 处理预览内容
    String preview = content;
    if (content.trim().startsWith('[') && content.trim().endsWith(']')) {
      try {
        // 尝试解析为JSON，以确认格式正确
        final json = jsonDecode(content);

        // 提取纯文本供预览使用
        String plainText = '';
        for (final op in json) {
          if (op.containsKey('insert')) {
            plainText += op['insert'].toString();
          }
        }

        // 使用纯文本作为预览
        preview = plainText;
      } catch (e) {
        // 如果解析失败，则直接使用内容作为预览
        logger.e('anki.llm_card.parse_prompt_json_failed'
            .trParams({'error': e.toString()}));
      }
    }

    promptObjList.add({
      'name': uniqueName,
      'content': content,
      'preview': preview,
      'createTime': now,
      'modifyTime': now,
    });
    _savePromptList();
  }

  // 检查提示词名称是否已存在
  bool _isPromptNameExists(String name) {
    return promptObjList.any((prompt) => prompt['name'] == name);
  }

  // Update an existing prompt
  void updatePrompt(int index, {String? name, String? content}) {
    if (index >= 0 && index < promptObjList.length) {
      final prompt = Map<String, dynamic>.from(promptObjList[index]);
      final oldName = prompt['name'] as String;

      // 如果提供了新名称，检查重名
      if (name != null && name != oldName) {
        String uniqueName = name;
        int suffix = 1;

        // 检查是否与其他提示词重名（排除自身）
        while (promptObjList
            .any((p) => p != promptObjList[index] && p['name'] == uniqueName)) {
          uniqueName = '$name ($suffix)';
          suffix++;
        }

        if (uniqueName != name) {
          logger.i('anki.llm_card.update_prompt_name_exists_renamed'
              .trParams({'name': name, 'uniqueName': uniqueName}));
          name = uniqueName;
        }

        prompt['name'] = name;
      }

      if (content != null) {
        prompt['content'] = content;

        // 检查是否是JSON格式，如果是，为了预览方便，也存储纯文本版
        if (content.trim().startsWith('[') && content.trim().endsWith(']')) {
          try {
            // 尝试解析为JSON，以确认格式正确
            final json = jsonDecode(content);

            // 提取纯文本供预览使用
            String plainText = '';
            for (final op in json) {
              if (op.containsKey('insert')) {
                plainText += op['insert'].toString();
              }
            }

            // 存储纯文本预览
            prompt['preview'] = plainText;
          } catch (e) {
            // 如果解析失败，则直接使用内容作为预览
            prompt['preview'] = content;
            logger.e('anki.llm_card.parse_prompt_json_failed'
                .trParams({'error': e.toString()}));
          }
        } else {
          // 如果不是JSON格式，直接使用内容作为预览
          prompt['preview'] = content;
        }
      }

      prompt['modifyTime'] = DateTime.now().toIso8601String();
      promptObjList[index] = prompt;
      _savePromptList();
    }
  }

  // Delete a prompt
  void deletePrompt(int index) {
    if (index >= 0 && index < promptObjList.length) {
      promptObjList.removeAt(index);
      _savePromptList();
    }
  }

  // Copy a prompt
  void copyPrompt(int index) {
    if (index >= 0 && index < promptObjList.length) {
      final prompt = Map<String, dynamic>.from(promptObjList[index]);
      final now = DateTime.now().toIso8601String();

      // 生成不重复的复制名称
      String baseName = prompt['name'] as String;
      String copyName = '$baseName ${'anki.llm_card.copy_suffix'.tr}';
      int suffix = 1;

      while (_isPromptNameExists(copyName)) {
        copyName = '$baseName ${'anki.llm_card.copy_suffix_numbered'.trParams({
              'number': suffix.toString()
            })}';
        suffix++;
      }

      final copyPrompt = {
        'name': copyName,
        'content': prompt['content'],
        'preview': prompt['preview'],
        'createTime': now,
        'modifyTime': now,
      };

      promptObjList.add(copyPrompt);
      _savePromptList();
    }
  }

  // 根据提示词名称获取对应的提示词内容
  String getPromptContentByName(String name) {
    // 如果提示词名称为空，返回默认系统提示词
    if (name.isEmpty) {
      return defaultSystemPrompt;
    }

    // 先从存储中重新加载最新的提示词列表
    final savedPrompts = _storage.read<List<dynamic>>(
      StorageBox.default_,
      LLMCardStorageKeys.promptList,
      [],
    );

    if (savedPrompts.isNotEmpty) {
      // 在最新的提示词列表中查找对应名称的提示词
      for (final item in savedPrompts) {
        final prompt = Map<String, dynamic>.from(item as Map);
        if (prompt['name'] == name) {
          logger.i(
              'anki.llm_card.get_prompt_from_storage'.trParams({'name': name}));
          return prompt['content'] as String;
        }
      }
    }

    // 如果在存储中未找到，尝试在内存中查找
    for (final prompt in promptObjList) {
      if (prompt['name'] == name) {
        return prompt['content'] as String;
      }
    }

    // 如果都未找到，返回默认系统提示词
    return defaultSystemPrompt;
  }

  void _loadProviderSettings(String provider) {
    // 加载特定提供商的API密钥
    final savedApiKey = _storage.read(StorageBox.default_,
        LLMCardStorageKeys.apiKeyForProvider(provider), '');
    if (apiKey.isNotEmpty) {
      apiKey.value = savedApiKey;
    } else {
      apiKey.value = '';
    }
    // 加载特定提供商的API端点
    final savedApiEndpoint = _storage.read(StorageBox.default_,
        LLMCardStorageKeys.apiEndpointForProvider(provider), '');
    if (savedApiEndpoint.isNotEmpty) {
      apiEndpoint.value = savedApiEndpoint;
    }
  }

  void _loadSavedSettings() {
    final provider =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.provider, 'guru');
    if (provider.isNotEmpty) {
      selectedModelProvider.value = provider;
    }

    final model =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.model, 'gpt-4o');
    if (model.isNotEmpty) {
      selectedModel.value = model;
    }

    final customModelValue =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.customModel, '');
    if (customModelValue.isNotEmpty) {
      customModel.value = customModelValue;
      customModel.value = customModelValue;
    }

    // 加载当前提供商的设置
    _loadProviderSettings(selectedModelProvider.value);

    final savedSystemPrompt = _storage.read(StorageBox.default_,
        LLMCardStorageKeys.systemPrompt, defaultSystemPrompt);
    if (systemPrompt.isNotEmpty) {
      systemPrompt.value = savedSystemPrompt;
    }

    final savedUserPrompt =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.userPrompt, '');
    if (userPrompt.isNotEmpty) {
      userPrompt.value = savedUserPrompt;
    }

    final temperatureValue = _storage.read(
        StorageBox.default_, LLMCardStorageKeys.temperature, '0.7');
    if (temperatureValue.isNotEmpty) {
      temperature.value = double.parse(temperatureValue);
    }

    final maxTokensValue = _storage.read(
        StorageBox.default_, LLMCardStorageKeys.maxTokens, '20000');
    if (maxTokensValue.isNotEmpty) {
      maxTokens.value = int.parse(maxTokensValue);
    }

    final maxConcurrentRequestsValue = _storage.read(
        StorageBox.default_, LLMCardStorageKeys.maxConcurrentRequests, '5');
    if (maxConcurrentRequestsValue.isNotEmpty) {
      maxConcurrentRequests.value = int.parse(maxConcurrentRequestsValue);
    }

    final promptNameValue =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.promptName, '');
    if (promptNameValue.isNotEmpty) {
      promptName.value = promptNameValue;
    }
    final apiKeyValue = _storage.read(StorageBox.default_,
        LLMCardStorageKeys.apiKeyForProvider(selectedModelProvider.value), '');
    if (apiKeyValue.isNotEmpty) {
      apiKey.value = apiKeyValue;
    }
    final logdirValue =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.logdir, '');
    if (logdirValue.isNotEmpty) {
      logdir.value = logdirValue;
    }

    final topPValue =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.topP, '0.95');
    if (topPValue.isNotEmpty) {
      topP.value = double.parse(topPValue);
    }
    final topKValue =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.topK, '10');
    if (topKValue.isNotEmpty) {
      topK.value = int.parse(topKValue);
    }
    final chunkSizeValue = _storage.read(
        StorageBox.default_, LLMCardStorageKeys.chunkSize, '5000');
    if (chunkSizeValue.isNotEmpty) {
      chunkSize.value = int.parse(chunkSizeValue);
    }
    final timeoutValue =
        _storage.read(StorageBox.default_, LLMCardStorageKeys.timeout, '1200');
    if (timeoutValue.isNotEmpty) {
      timeout.value = int.parse(timeoutValue);
    }
  }

  void saveSettings({bool showToast = true}) {
    // 保存API密钥
    _storage.write(
        StorageBox.default_,
        LLMCardStorageKeys.apiKeyForProvider(selectedModelProvider.value),
        apiKey.value);
    // 保存API端点
    _storage.write(
        StorageBox.default_,
        LLMCardStorageKeys.apiEndpointForProvider(selectedModelProvider.value),
        apiEndpoint.value);
    // 保存提供商
    _storage.write(StorageBox.default_, LLMCardStorageKeys.provider,
        selectedModelProvider.value);
    // 保存模型
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.model, selectedModel.value);
    // 保存自定义模型
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.customModel, customModel.value);
    // 保存系统提示词
    _storage.write(StorageBox.default_, LLMCardStorageKeys.systemPrompt,
        systemPrompt.value);
    // 保存用户提示词
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.userPrompt, userPrompt.value);
    // 保存温度
    _storage.write(StorageBox.default_, LLMCardStorageKeys.temperature,
        temperature.value.toString());
    // 保存最大tokens
    _storage.write(StorageBox.default_, LLMCardStorageKeys.maxTokens,
        maxTokens.value.toString());
    // 保存提示词名称
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.promptName, promptName.value);
    // 保存最大并发请求数
    _storage.write(
        StorageBox.default_,
        LLMCardStorageKeys.maxConcurrentRequests,
        maxConcurrentRequests.value.toString());
    // 保存日志目录
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.logdir, logdir.value);
    // 保存topP
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.topP, topP.value.toString());
    // 保存topK
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.topK, topK.value.toString());
    // 保存文本分块大小
    _storage.write(StorageBox.default_, LLMCardStorageKeys.chunkSize,
        chunkSize.value.toString());
    // 保存超时
    _storage.write(StorageBox.default_, LLMCardStorageKeys.timeout,
        timeout.value.toString());
    // 保存挖空模式
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.clozeMode, clozeMode.value);
    // 保存一空一卡
    _storage.write(StorageBox.default_, LLMCardStorageKeys.isPerClozePerCard,
        isPerClozePerCard.value);
    // 保存HTML转义
    _storage.write(StorageBox.default_, LLMCardStorageKeys.isHtmlEscape,
        isHtmlEscape.value);
    // 使用AI标签
    _storage.write(
        StorageBox.default_, LLMCardStorageKeys.useAITags, useAITags.value);
    // showToastNotification(null, '提示', '设置已保存');
  }

  void updateModelProvider(String? value) {
    if (value != null) {
      selectedModelProvider.value = value;
      if (modelList[value]?.isNotEmpty ?? false) {
        selectedModel.value = modelList[value]![0]['value'] as String;
      }
      if (value == "openrouter") {
        apiEndpoint.value = "https://openrouter.ai/api/v1/";
      } else if (value == "guru") {
        apiEndpoint.value = "https://ai.kevin2li.top/v1/";
      } else if (value == "siliconflow") {
        apiEndpoint.value = "https://api.siliconflow.cn/v1/";
      } else if (value == "closeai") {
        if (selectedProtocol.value == "google") {
          apiEndpoint.value = "https://api.openai-proxy.org/google/";
        } else if (selectedProtocol.value == "anthropic") {
          apiEndpoint.value = "https://api.openai-proxy.org/anthropic/";
        } else {
          apiEndpoint.value = "https://api.openai-proxy.org/v1/";
        }
      } else {
        apiEndpoint.value = "";
      }
    }
  }

  void updateModel(String? value) {
    if (value != null) {
      selectedModel.value = value;
    }
  }

  // Get model name based on card type
  Map<String, String> getModelNames() {
    Map<String, String> modelNames = {
      "qa": "Kevin Text QA Card v2",
      "cloze": "Kevin Text Cloze v3",
      "choice": "Kevin Choice Card v2",
      "multi_choice": "Kevin Choice Card v2",
      "judge": "Kevin Choice Card v2",
    };
    return modelNames;
  }

  // Generate prompt based on card types and user input
  Future<String> generatePrompt() async {
    return PromptGenerator.generatePrompt(
      cardTypes: cardTypes,
      userPrompt: userPrompt.value,
      difficulties: difficulties.value,
      clozeMode: clozeMode.value,
      isPerClozePerCard: isPerClozePerCard.value,
      isAutoTag: isAutoTag.value,
      isAnswerCloze: isAnswerCloze.value,
    );
  }

  // Load prompt sorting option from storage
  void _loadPromptSortOption() {
    final savedSortOption = _storage.read(
      StorageBox.default_,
      LLMCardStorageKeys.promptSortOption,
      'modifyTime',
    );

    if (savedSortOption.isNotEmpty) {
      promptSortOption.value = savedSortOption;
    }
  }

  // Save prompt sorting option to storage
  void _savePromptSortOption() {
    _storage.write(
      StorageBox.default_,
      LLMCardStorageKeys.promptSortOption,
      promptSortOption.value,
    );
  }

  // 获取卡片类型的中文名称
  String getCardTypeDisplayName(String cardType) {
    return PromptGenerator.getCardTypeDisplayName(cardType);
  }

  // 获取卡片难度的中文名称
  String getDifficultyDisplayName(String difficultyLevel) {
    return PromptGenerator.getDifficultyDisplayName(difficultyLevel);
  }

  // Submit the form and generate cards
  void submit(BuildContext context) async {
    if (tabController.selected == "card") {
      // 验证输入
      if (apiKey.value.trim().isEmpty) {
        showToastNotification(null, 'anki.llm_card.error'.tr,
            'anki.llm_card.api_key_cannot_empty'.tr,
            type: "error");
        return;
      }

      if (selectedModelProvider.value == 'custom' &&
          (customModel.value.trim().isEmpty ||
              apiEndpoint.value.trim().isEmpty)) {
        showToastNotification(null, 'anki.llm_card.error'.tr,
            'anki.llm_card.custom_model_required'.tr,
            type: "error");
        return;
      }

      if (cardTypes.isEmpty) {
        showToastNotification(null, 'anki.llm_card.error'.tr,
            'anki.llm_card.select_at_least_one_card_type'.tr,
            type: "error");
        return;
      }

      if (selectedFilePaths.isEmpty) {
        showToastNotification(null, 'anki.llm_card.error'.tr,
            'anki.llm_card.select_at_least_one_file'.tr,
            type: "error");
        return;
      }

      if (promptName.value.isEmpty) {
        showToastNotification(
            null, 'anki.llm_card.error'.tr, 'anki.llm_card.select_prompt'.tr,
            type: "error");
        return;
      }

      // 设置进度对话框
      final exportMode = settingController.getExportCardMode();
      progressController.reset(
        showOutputHint: exportMode == "apkg",
        numberButtons: exportMode == "apkg" ? 2 : 0,
      );
      progressController.showProgressDialog(context);

      try {
        final outputPath = await PathUtils.getOutputApkgPath();
        final cardTypeModels = getModelNames(); // 获取卡片类型到模型的映射
        String finalApiEndpoint = apiEndpoint.value;
        if (!finalApiEndpoint.endsWith("/")) {
          finalApiEndpoint = "$finalApiEndpoint/";
        }
        // 构建请求参数
        final params = {
          'backend': selectedProtocol.value,
          'model': selectedModelProvider.value == 'custom'
              ? customModel.value
              : selectedModel.value,
          'api_key': apiKey.value,
          'base_url': finalApiEndpoint,
          'system_prompt': promptName.value.isEmpty
              ? defaultSystemPrompt
              : getPromptContentByName(promptName.value),
          'card_type_models': cardTypeModels,
          'temperature': temperature.value,
          'max_tokens': maxTokens.value,
          'parent_deck': parentDeck.value,
          'tags': tags.value,
          'attachments': selectedFilePaths,
          'output_path': outputPath,
          'max_concurrent_requests': maxConcurrentRequests.value,
          'logdir': logdir.value,
          'reasoning': isReasoning.value,
          'chunk_size': chunkSize.value,
          'page_range': pageRange.value,
          'cloze_mode': clozeMode.value,
          'is_per_cloze_per_card': isPerClozePerCard.value,
          'is_html_escape': isHtmlEscape.value,
          'use_ai_tags': useAITags.value,
        };

        // 调用后端生成卡片
        final resp =
            await messageController.request(params, 'llm/generate_cards');

        if (resp.status == "success") {
          if (settingController.getExportCardMode() == "ankiconnect") {
            await AnkiConnectController().importApkg(resp.data, isDelete: true);
          }
          progressController.outputPath.value = outputPath;
          progressController.updateProgress(status: "completed", message: 'common.completed'.tr);
        } else {
          progressController.updateProgress(
              status: "error", message: resp.message);
        }
      } catch (e) {
        progressController.updateProgress(
            status: "error", message: e.toString());
      }
    } else if (tabController.selected == "prompt") {
      // 提示词保存逻辑
      if (promptNameInput.value.isEmpty) {
        showToastNotification(null, 'anki.llm_card.error'.tr,
            'anki.llm_card.prompt_name_cannot_empty'.tr,
            type: "error");
        return;
      }

      try {
        final fullPrompt = await generatePrompt();
        addPrompt(promptNameInput.value, fullPrompt);
        promptNameInput.value = '';
        showToastNotification(
            null, 'anki.llm_card.success'.tr, 'anki.llm_card.prompt_saved'.tr,
            type: "success");
      } catch (e) {
        showToastNotification(
            null,
            'anki.llm_card.error'.tr,
            'anki.llm_card.generate_prompt_failed'
                .trParams({'error': e.toString()}),
            type: "error");
      }
    }
  }
}
