import 'dart:convert';
import 'dart:io';
import 'package:anki_guru/controllers/anki/ocr/ocr_parser.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:hotkey_manager/hotkey_manager.dart';
import 'package:pasteboard/pasteboard.dart';
import 'package:ulid/ulid.dart';
import 'dart:ui' as ui;

import 'package:screen_capturer/screen_capturer.dart';

class ImageCard {
  Uint8List imageData;
  Uint8List coverData;
  final TextEditingController noteController;
  final TextEditingController tagController;
  final TextEditingController clozeController;
  String clozeData;
  String groupData; // Store mask group assignments as JSON
  String ocrText;

  ImageCard({
    required this.imageData,
    Uint8List? coverData,
    required this.noteController,
    required this.tagController,
    required this.clozeController,
    String? clozeData,
    String? groupData,
    this.ocrText = "",
  })  : coverData = coverData ?? imageData,
        clozeData = clozeData ?? "",
        groupData = groupData ?? "";
}

class ImageCardController extends GetxController {
  final _storage = StorageManager();
  final settingController = Get.find<SettingController>();
  final messageController = Get.find<MessageController>();
  final progressController = Get.find<ProgressController>();

  // 配置项
  final parentDeck = ''.obs;
  final tags = ''.obs;
  final clozeMode = "free_guess".obs;
  final oneClozePeCard = false.obs;
  final autoOCR = false.obs;
  final primaryColor = const Color(0xFFFF5656).obs;
  final secondaryColor = const Color(0xFFFFEBA2).obs;

  final allDecks = <String>[].obs;
  final imageEditorKey = GlobalKey<ProImageEditorState>();

  final imageFile = Rx<File?>(null);
  final byteArray = Rx<Uint8List?>(null);

  final RxList<ImageCard> imageCards = <ImageCard>[].obs;

  final pageController = PageController();
  final currentPage = 0.obs;
  final isProcessing = false.obs;

  final snapHotKey = Rxn<HotKey>();
  final pasteHotKey = Rxn<HotKey>();
  final leftArrowHotKey = Rxn<HotKey>();
  final rightArrowHotKey = Rxn<HotKey>();

  final editorTitle = "anki.image_card.image_cloze".tr.obs;

  // 添加视图模式状态
  final isGridView = false.obs;

  // 添加一个变量来保存进入宫格视图前的页码
  int? _lastPageBeforeGrid;

  @override
  void onInit() {
    super.onInit();
    loadSettings();
    _registerKeyboardListeners();
  }

  // 新增：用于启动图片批注编辑器的方法
  Future<void> openImageForAnnotation(int index) async {
    final card = imageCards[index];

    // 使用 pro_image_editor 打开图片
    final editedImageBytes = await Get.to(
      () => ProImageEditor.memory(
        card.imageData,
        callbacks: ProImageEditorCallbacks(
          onImageEditingComplete: (bytes) async {
            Get.back(result: bytes);
          },
        ),
        configs: const ProImageEditorConfigs(
          paintEditor: PaintEditorConfigs(),
          textEditor: TextEditorConfigs(enabled: false),
          cropRotateEditor: CropRotateEditorConfigs(enabled: false),
          filterEditor: FilterEditorConfigs(enabled: false),
          emojiEditor: EmojiEditorConfigs(enabled: false),
          stickerEditor: StickerEditorConfigs(enabled: false),
        ),
      ),
    );

    // 如果编辑器返回了新的图片数据，则更新卡片
    if (editedImageBytes != null && editedImageBytes is Uint8List) {
      // imageData is now mutable and can be updated.
      card.imageData = editedImageBytes;
      // 封面也应该更新为批注后的图片
      card.coverData = editedImageBytes;
      // 强制刷新列表以显示更新后的图片
      imageCards.refresh();
    }
  }

  void loadSettings() {
    // 从存储中读取设置
    parentDeck.value = _storage.read(
        StorageBox.imageCard, 'default_deck', 'anki.image_card.guru_import'.tr);

    tags.value = _storage.read(
        StorageBox.imageCard, ImageCardStorageKeys.defaultTags, '');

    clozeMode.value = _storage.read(StorageBox.imageCard,
        ImageCardStorageKeys.defaultClozeMode, 'free_guess');

    oneClozePeCard.value = _storage.read(
        StorageBox.imageCard, ImageCardStorageKeys.oneClozePerCard, false);

    // Load autoOCR setting
    autoOCR.value = _storage.read(
        StorageBox.imageCard, ImageCardStorageKeys.autoOCR, false);

    // Load color settings
    final savedPrimaryColor = _storage.read(
        StorageBox.imageCard, ImageCardStorageKeys.primaryColor, 0xFFFF5656);
    primaryColor.value = Color(savedPrimaryColor);

    final savedSecondaryColor = _storage.read(
        StorageBox.imageCard, ImageCardStorageKeys.secondaryColor, 0xFFFFEBA2);
    secondaryColor.value = Color(savedSecondaryColor);

    initHotKey();
    _setupColorListeners();
  }

  /// Setup listeners for automatic color persistence
  void _setupColorListeners() {
    // Listen to primaryColor changes and automatically save to storage
    primaryColor.listen((Color color) {
      _storage.write(
        StorageBox.imageCard,
        ImageCardStorageKeys.primaryColor,
        color.toARGB32(),
      );
    });

    // Listen to secondaryColor changes and automatically save to storage
    secondaryColor.listen((Color color) {
      _storage.write(
        StorageBox.imageCard,
        ImageCardStorageKeys.secondaryColor,
        color.toARGB32(),
      );
    });
  }

  Future<void> initHotKey() async {
    // Load snap hotkey - only on macOS
    if (Platform.isMacOS) {
      final savedSnapHotkey = _storage.read(
        StorageBox.imageCard,
        ImageCardStorageKeys.snapHotKey,
        "",
      );

      if (savedSnapHotkey.isNotEmpty) {
        logger.i('savedSnapHotkey: $savedSnapHotkey');
        try {
          // 从字符串恢复 HotKey 对象
          snapHotKey.value =
              HotKey.fromJson(jsonDecode(savedSnapHotkey.toString()));
          setSnapHotKey(snapHotKey.value);
        } catch (e) {
          logger.e('Error loading snap hotkey: $e');
        }
      }
    }

    // Load paste hotkey
    final savedPasteHotkey = _storage.read(
      StorageBox.imageCard,
      ImageCardStorageKeys.pasteHotKey,
      "",
    );

    if (savedPasteHotkey.isNotEmpty) {
      logger.i('savedPasteHotkey: $savedPasteHotkey');
      try {
        // 从字符串恢复 HotKey 对象
        pasteHotKey.value =
            HotKey.fromJson(jsonDecode(savedPasteHotkey.toString()));
        setPasteHotKey(pasteHotKey.value);
      } catch (e) {
        logger.e('Error loading paste hotkey: $e');
      }
    }
  }

  Future<void> saveSettings() async {
    // 将设置保存到存储中
    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.defaultDeck,
      parentDeck.value,
    );

    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.defaultTags,
      tags.value,
    );

    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.defaultClozeMode,
      clozeMode.value,
    );

    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.oneClozePerCard,
      oneClozePeCard.value,
    );

    // Save autoOCR setting
    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.autoOCR,
      autoOCR.value,
    );

    // Save color settings (redundant due to automatic listeners, but kept for consistency)
    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.primaryColor,
      primaryColor.value.toARGB32(),
    );

    _storage.write(
      StorageBox.imageCard,
      ImageCardStorageKeys.secondaryColor,
      secondaryColor.value.toARGB32(),
    );

    if (snapHotKey.value != null) {
      // 将 HotKey 对象转换为字符串保存
      logger.d('snapHotKey: ${snapHotKey.value?.toJson()}');
      _storage.write(
        StorageBox.imageCard,
        ImageCardStorageKeys.snapHotKey,
        jsonEncode(snapHotKey.value!.toJson()),
      );
    } else {
      _storage.remove(StorageBox.imageCard, ImageCardStorageKeys.snapHotKey);
    }

    if (pasteHotKey.value != null) {
      // 将 HotKey 对象转换为字符串保存
      logger.d('pasteHotKey: ${pasteHotKey.value?.toJson()}');
      _storage.write(
        StorageBox.imageCard,
        ImageCardStorageKeys.pasteHotKey,
        jsonEncode(pasteHotKey.value!.toJson()),
      );
    } else {
      _storage.remove(
        StorageBox.imageCard,
        ImageCardStorageKeys.pasteHotKey,
      );
    }

    // 显示通知
    Get.showSnackbar(
      GetSnackBar(
        message: 'anki.image_card.settings_saved'.tr,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> pickImageFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
        compressionQuality: 0,
      );

      if (result != null) {
        for (var file in result.files) {
          if (file.path != null) {
            final imageBytes = await File(file.path!).readAsBytes();
            imageCards.add(ImageCard(
              imageData: imageBytes,
              noteController: TextEditingController(),
              tagController: TextEditingController(),
              clozeController: TextEditingController(),
            ));
          }
        }
      }
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.image_card.file_selection_failed'.tr,
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
    }
  }

  Future<void> captureScreen() async {
    // Only allow screenshot functionality on macOS
    if (!Platform.isMacOS) {
      logger.w("Screenshot functionality is only supported on macOS platform");
      Get.showSnackbar(
        const GetSnackBar(
          message: 'Screenshot functionality is only supported on macOS',
          duration: Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
      return;
    }

    try {
      // Generate temporary file path for screenshot
      final tempDir = await getTemporaryDirectory();
      final fileName = 'screenshot_${DateTime.now().millisecondsSinceEpoch}.png';
      final tempFilePath = p.join(tempDir.path, fileName);

      CapturedData? capturedData = await screenCapturer.capture(
        mode: CaptureMode.region, // screen, window
        imagePath: tempFilePath,
        copyToClipboard: true,
      );

      logger.i("Screenshot captured: $capturedData");

      // Check if capture was successful and file exists
      if (capturedData != null && capturedData.imagePath != null) {
        final capturedFile = File(capturedData.imagePath!);
        if (await capturedFile.exists()) {
          // Read the captured image data
          final imageBytes = await capturedFile.readAsBytes();

          // Add the captured image to imageCards collection
          imageCards.add(ImageCard(
            imageData: imageBytes,
            noteController: TextEditingController(),
            tagController: TextEditingController(),
            clozeController: TextEditingController(),
          ));

          // Navigate to the newly added image
          final newIndex = imageCards.length - 1;
          if (newIndex > 0) {
            currentPage.value = newIndex;
            pageController.animateToPage(
              newIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }

          // Clean up temporary file
          try {
            await capturedFile.delete();
          } catch (deleteError) {
            logger.w("Failed to delete temporary screenshot file: $deleteError");
          }

          // Show success message
          Get.showSnackbar(
            GetSnackBar(
              message: 'anki.image_card.image_added_from_capture'.tr,
              duration: const Duration(seconds: 2),
              snackPosition: SnackPosition.BOTTOM,
            ),
          );

          logger.i("Screenshot successfully added to image cards collection");
        } else {
          logger.e("Screenshot file does not exist: ${capturedData.imagePath}");
          Get.showSnackbar(
            GetSnackBar(
              message: 'anki.image_card.file_selection_failed'.tr,
              duration: const Duration(seconds: 2),
              snackPosition: SnackPosition.BOTTOM,
            ),
          );
        }
      } else {
        logger.e("Screenshot capture failed or was cancelled");
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.image_card.file_selection_failed'.tr,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
          ),
        );
      }
    } catch (e) {
      logger.e("Screenshot capture error: $e");
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.image_card.file_selection_failed'.tr,
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
    }
  }

  bool validateAll() {
    return true;
  }

  void removeImage(int index) {
    imageCards.removeAt(index);
    if (_lastPageBeforeGrid != null) {
      if (_lastPageBeforeGrid! >= imageCards.length) {
        _lastPageBeforeGrid = imageCards.isEmpty ? 0 : imageCards.length - 1;
      } else if (_lastPageBeforeGrid! > index) {
        _lastPageBeforeGrid = _lastPageBeforeGrid! - 1;
      }
    }
    if (currentPage.value >= imageCards.length) {
      currentPage.value = imageCards.isEmpty ? 0 : imageCards.length - 1;
    }
  }

  Future<void> submitCards(BuildContext context) async {
    if (imageCards.isEmpty) {
      showToastNotification(
          context, "anki.image_card.please_add_images_first".tr, "",
          type: "error");
      return;
    }

    final exportCardMode = settingController.getExportCardMode();
    logger.i("exportCardMode: $exportCardMode");
    logger.i("autoOCR: ${autoOCR.value}");
    if (exportCardMode == "apkg" || exportCardMode == "ankiconnect") {
      progressController.reset(
        showOutputHint: exportCardMode == "apkg",
        numberButtons: exportCardMode == "apkg" ? 2 : 0,
      );
      progressController.showProgressDialog(context);
    }

    // Start OCR processing in parallel if enabled
    Future<void>? ocrFuture;
    if (autoOCR.value) {
      ocrFuture = _processAllOCR();
    }

    try {
      final parentDeck = _storage.read(StorageBox.imageCard,
          ImageCardStorageKeys.defaultDeck, "anki.image_card.guru_import".tr);
      final clozeMode = _storage.read(StorageBox.imageCard,
          ImageCardStorageKeys.defaultClozeMode, "free_guess");
      final isOneClozePerCard = _storage.read(
          StorageBox.imageCard, ImageCardStorageKeys.oneClozePerCard, false);
      final baseTags = _storage
          .read(StorageBox.imageCard, ImageCardStorageKeys.defaultTags, "")
          .toString()
          .split(',')
          .where((tag) => tag.isNotEmpty)
          .toList();

      List<String> imageList = [];
      List<AnkiNote> notes = [];
      if (exportCardMode == "apkg") {
        final jsPath = await AssetStore.getAssetAbsolutePath("ankiPersistence");
        if (jsPath.isNotEmpty) {
          imageList.add(jsPath);
        }
      }

      // Prepare card data
      for (var card in imageCards) {
        // 1. 先添加图片媒体
        final imagePath = await _saveImageToTemp(card.imageData);
        imageList.add(imagePath);
        final fileName = p.basename(imagePath);
        String mediaRes = "<img src='$fileName' />";
        if (exportCardMode == "ankidroid") {
          final resp =
              await AnkiConnectController().addMediaToAnkiDroid(imagePath);
          if (resp.status == "success") {
            mediaRes = resp.data;
          }
        }
        // 安全地处理标签
        List<String> tags = [];
        try {
          tags = card.tagController.text
              .split(',')
              .where((tag) => tag.trim().isNotEmpty)
              .map((tag) => tag.trim())
              .toList();
        } catch (e) {
          logger.w("Error parsing tags: $e");
        }
        tags.addAll(baseTags);

        final id = "image_${Ulid().toString()}";
        final extraInfo = card.noteController.text;

        if ((clozeMode == "mask_one_guess_one" ||
                clozeMode == "mask_all_guess_one") &&
            isOneClozePerCard) {
          var mode = "${clozeMode}_multi";
          if (card.clozeData.isNotEmpty) {
            final obj = jsonDecode(card.clozeData);
            for (var i = 0; i < obj.length; i++) {
              // Format color data as comma-separated hex codes
              final primaryHex = primaryColor.value.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase();
              final secondaryHex = secondaryColor.value.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase();
              final formattedColorData = "#$primaryHex,#$secondaryHex";

              notes.add(AnkiNote(
                deckName: parentDeck,
                modelName: InternalModelName.KEVIN_IMAGE_CLOZE,
                fields: [
                  "${id}_${i + 1}",
                  "",
                  mediaRes,
                  card.ocrText,
                  card.clozeData,
                  "",
                  extraInfo,
                  mode,
                  "c${i + 1}",
                  formattedColorData,
                  "",
                ],
                tags: tags,
              ));
            }
          }
        } else {
          // Format color data as comma-separated hex codes
          final primaryHex = primaryColor.value.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase();
          final secondaryHex = secondaryColor.value.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase();
          final formattedColorData = "#$primaryHex,#$secondaryHex";

          notes.add(AnkiNote(
            deckName: parentDeck,
            modelName: InternalModelName.KEVIN_IMAGE_CLOZE,
            fields: [
              id,
              "",
              mediaRes,
              card.ocrText, // This will be updated if OCR is enabled
              card.clozeData.isEmpty ? "[]" : card.clozeData,
              "",
              extraInfo,
              clozeMode,
              "",
              formattedColorData,
              "",
            ],
            tags: tags,
          ));
        }
      }

      // Wait for OCR to complete before finalizing if it was started
      if (ocrFuture != null) {
        // 明确等待OCR处理完成
        await ocrFuture;
        // 强制刷新以确保最新数据可用
        imageCards.refresh();

        // 创建一个map将图片文件名映射到OCR结果
        Map<String, String> fileToOcrMap = {};
        for (int i = 0; i < imageCards.length && i < imageList.length; i++) {
          // 提取图片文件名
          String fileName = p.basename(imageList[i]);
          // 存储OCR结果
          fileToOcrMap[fileName] = imageCards[i].ocrText;
        }

        // 更新所有notes的OCR字段
        for (int i = 0; i < notes.length; i++) {
          // 从mediaRes中提取文件名
          final mediaRes = notes[i].fields[2]; // mediaRes字段
          final fileNameMatch = RegExp(r"src='([^']+)'").firstMatch(mediaRes);

          if (fileNameMatch != null) {
            final fileName = fileNameMatch.group(1); // 提取文件名
            if (fileName != null && fileToOcrMap.containsKey(fileName)) {
              // 使用文件名查找对应的OCR结果
              notes[i].fields[3] = fileToOcrMap[fileName]!;
            }
          }
        }
      }

      // Generate cards
      if (exportCardMode == "apkg") {
        final outputPath = await PathUtils.getOutputApkgPath(name: parentDeck);
        final resp = await AnkiConnectController().genApkg(
            notes, imageList, outputPath,
            internalMediaTypes: ["image_cloze_card"]);
        if (resp.status == "success") {
          progressController.updateProgress(status: "completed");
          progressController.outputPath.value = resp.data;
        } else {
          progressController.updateProgress(status: "error", message: resp.message);
        }
      } else if (exportCardMode == "ankidroid") {
        final resp = await AnkiConnectController().uploadToAnkiDroid(notes);
        logger.d("uploadToAnkiDroid resp: $resp");
        if (resp.status == "success") {
          showToastNotification(
              context,
              "anki.image_card.success".tr,
              "anki.image_card.cards_added_to_anki"
                  .trParams({'count': notes.length.toString()}),
              type: "success");
        } else {
          showToastNotification(
              context, "anki.image_card.failed".tr, resp.message,
              type: "error");
        }
      } else if (exportCardMode == "ankiconnect") {
        final outputPath = await PathUtils.getOutputApkgPath(name: parentDeck);
        final resp =
            await AnkiConnectController().genApkg(notes, imageList, outputPath);
        if (resp.status == "success") {
          await AnkiConnectController().importApkg(resp.data, isDelete: true);
          progressController
              .updateProgress(status: "completed", message: "");
        } else {
          progressController.updateProgress(status: "error", message: resp.message);
        }
      }
    } on PlatformException catch (e) {
      logger.e("Platform error: ${e.message}");
      showToastNotification(
          context, "anki.image_card.failed".tr, "${e.message}",
          type: "error");
    } catch (e) {
      logger.e("Error submitting cards: $e");
      if (exportCardMode == "apkg") {
        progressController.updateProgress(status: "error", message: e.toString());
      } else {
        showToastNotification(context, "anki.image_card.failed".tr, "$e",
            type: "error");
      }
    }
  }

  // 将图片数据保存为临时文件并返回路径
  Future<String> _saveImageToTemp(Uint8List imageData) async {
    final tempDir = await getTemporaryDirectory();
    final fileName = 'image_${DateTime.now().millisecondsSinceEpoch}.png';
    final tempFilePath = p.join(tempDir.path, fileName);

    // Convert to ui.Image for proper formatting
    final codec = await ui.instantiateImageCodec(imageData);
    final frame = await codec.getNextFrame();
    final image = frame.image;

    // Convert back to PNG bytes
    final pngBytes = await image.toByteData(format: ui.ImageByteFormat.png);
    if (pngBytes == null) {
      throw Exception('Failed to convert image to PNG format');
    }

    // Save as PNG file
    final tempFile = File(tempFilePath);
    await tempFile.writeAsBytes(pngBytes.buffer.asUint8List());
    return tempFile.path;
  }

  void addCloze(int index) {
    byteArray.value = imageCards[index].imageData;
    editorTitle.value = "${index + 1} / ${imageCards.length}";
    Get.toNamed("/image_editor");
  }

  void nextPage() {
    if (currentPage.value < imageCards.length - 1) {
      // 在image_editor路由中，只更新索引和标题，不调用pageController
      // 因为在编辑器中我们使用Focus小部件监听键盘事件自行处理导航逻辑
      if (Get.currentRoute == "/image_editor") {
        currentPage.value++;
        editorTitle.value = "${currentPage.value + 1} / ${imageCards.length}";
        logger.i("nextPage in editor: ${currentPage.value}");
      } else {
        // 在其他路由中使用正常的页面切换动画
        pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void previousPage() {
    if (currentPage.value > 0) {
      // 在image_editor路由中，只更新索引和标题，不调用pageController
      // 因为在编辑器中我们使用Focus小部件监听键盘事件自行处理导航逻辑
      if (Get.currentRoute == "/image_editor") {
        currentPage.value--;
        editorTitle.value = "${currentPage.value + 1} / ${imageCards.length}";
        logger.i("previousPage in editor: ${currentPage.value}");
      } else {
        // 在其他路由中使用正常的页面切换动画
        pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void removeAllImages() {
    imageCards.clear();
    currentPage.value = 0;
    _lastPageBeforeGrid = null;
  }

  @override
  void onClose() {
    // 取消注册热键
    if (Platform.isAndroid || Platform.isIOS) {
      // 移动平台不需要处理
    } else {
      if (leftArrowHotKey.value != null) {
        hotKeyManager.unregister(leftArrowHotKey.value!);
      }
      if (rightArrowHotKey.value != null) {
        hotKeyManager.unregister(rightArrowHotKey.value!);
      }
      // Unregister snap hotkey (only on macOS)
      if (Platform.isMacOS && snapHotKey.value != null) {
        hotKeyManager.unregister(snapHotKey.value!);
      }
      // Unregister paste hotkey
      if (pasteHotKey.value != null) {
        hotKeyManager.unregister(pasteHotKey.value!);
      }
    }
    pageController.dispose();
    super.onClose();
  }

  // 修改热键注册的通用方法
  Future<void> _registerHotKey(
    Rxn<HotKey> hotKeyVar,
    HotKey? newHotKey,
    String storageKey,
    Future<void> Function(HotKey) handler,
  ) async {
    if (Platform.isAndroid || Platform.isIOS) {
      return;
    }
    // 取消注册旧的热键
    if (hotKeyVar.value != null) {
      // 创建一个副本来避免并发修改错误
      final registeredHotKeyList = [...hotKeyManager.registeredHotKeyList];
      for (var hotKey in registeredHotKeyList) {
        if (hotKey.identifier == hotKeyVar.value!.identifier) {
          await hotKeyManager.unregister(hotKey);
        }
      }
    }

    if (newHotKey != null) {
      hotKeyVar.value = newHotKey;
      await hotKeyManager.register(
        newHotKey,
        keyDownHandler: (hotKey) => handler(hotKey),
      );
      // 保存热键设置
      _storage.write(
        StorageBox.imageCard,
        storageKey,
        jsonEncode(newHotKey.toJson()),
      );
    } else {
      hotKeyVar.value = null;
      _storage.remove(StorageBox.imageCard, storageKey);
    }
  }

  // 修改 setSnapHotKey - only register on macOS
  void setSnapHotKey(HotKey? newHotKey) async {
    // Only allow snap hotkey registration on macOS
    if (!Platform.isMacOS) {
      logger.w("Snap hotkey functionality is only supported on macOS platform");
      return;
    }

    await _registerHotKey(
      snapHotKey,
      newHotKey,
      ImageCardStorageKeys.snapHotKey,
      _handleSnapHotKey,
    );
  }

  // 修改 setPasteHotKey
  void setPasteHotKey(HotKey? newHotKey) async {
    await _registerHotKey(
      pasteHotKey,
      newHotKey,
      ImageCardStorageKeys.pasteHotKey,
      _handlePasteHotKey,
    );
  }

  // 获取人类可读的快捷键字符串
  String getHotKeyDisplayText(HotKey? hotKey) {
    if (hotKey == null) return 'anki.image_card.not_set'.tr;
    final modifierNames = (hotKey.modifiers ?? []).map((m) {
      switch (m) {
        case HotKeyModifier.alt:
          return 'Alt';
        case HotKeyModifier.control:
          return 'Ctrl';
        case HotKeyModifier.meta:
          if (Platform.isMacOS) {
            return 'Cmd';
          } else {
            return 'Win';
          }
        case HotKeyModifier.shift:
          return 'Shift';
        case HotKeyModifier.capsLock:
          return 'CapsLock';
        case HotKeyModifier.fn:
          return 'Fn';
      }
    }).where((name) => name.isNotEmpty);

    // 获取主键名称（去掉"Key "前缀）
    String keyName = keyNames[hotKey.physicalKey.usbHidUsage] ?? '';
    if (keyName.startsWith('Key ')) {
      keyName = keyName.substring(4);
    }
    if (keyName.startsWith('Digit ')) {
      keyName = keyName.substring(6);
    }

    // 组合所有部分
    final parts = [...modifierNames, keyName];
    return parts.join(' + ');
  }

  Future<void> _handleSnapHotKey(HotKey hotKey) async {
    logger.d("snap hotKey triggered: ${hotKey.toJson()} ");
    await captureScreen();
  }

  Future<void> _handlePasteHotKey(HotKey hotKey) async {
    logger.d("paste hotKey triggered: ${hotKey.toJson()}");
    await loadImageFromClipboard();
  }

  Future<void> loadImageFromClipboard() async {
    try {
      Uint8List? imageBytes;
      if (Platform.isAndroid) {
        const platform = MethodChannel('samples.flutter.dev/battery');
        final result = await platform.invokeMethod<dynamic>(
          'getClipboardData',
        );
        // logger.d("clipboard data: $result");
        if (result != null && result['type'] == 'image') {
          // 移除所有换行符后再解码
          final base64String =
              result['content'].toString().replaceAll(RegExp(r'\s+'), '');
          imageBytes = base64Decode(base64String);
        }
      } else {
        imageBytes = await Pasteboard.image;
      }
      if (imageBytes != null) {
        imageCards.add(ImageCard(
          imageData: imageBytes,
          noteController: TextEditingController(),
          tagController: TextEditingController(),
          clozeController: TextEditingController(),
        ));

        // 切换到新添加的图片
        final newIndex = imageCards.length - 1;
        if (newIndex > 0) {
          currentPage.value = newIndex;
          pageController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }

        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.image_card.image_added_from_clipboard'.tr,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      logger.e("Error reading image from clipboard: $e");
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.image_card.failed_to_read_image_from_clipboard'.tr,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // 修改视图模式切换方法
  void toggleViewMode() {
    if (!isGridView.value) {
      // 切换到宫格视图前保存当前页码
      _lastPageBeforeGrid = currentPage.value;
    } else {
      // 从宫格视图切换回普通视图时恢复之前的页码
      if (_lastPageBeforeGrid != null) {
        currentPage.value = _lastPageBeforeGrid!;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          pageController.jumpToPage(_lastPageBeforeGrid!);
        });
      }
    }
    isGridView.value = !isGridView.value;
  }

  // 修改切换到单页视图的方法
  void switchToPageView(int index) {
    isGridView.value = false;
    _lastPageBeforeGrid = null; // 清除保存的页码，因为是直接选择了新页面
    currentPage.value = index;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      pageController.jumpToPage(index);
    });
  }

  void _registerKeyboardListeners() {
    if (Platform.isAndroid || Platform.isIOS) {
      return;
    }

    // 注册左方向键
    leftArrowHotKey.value = HotKey(
      key: PhysicalKeyboardKey.arrowLeft,
      scope: HotKeyScope.inapp,
    );

    // 注册右方向键
    rightArrowHotKey.value = HotKey(
      key: PhysicalKeyboardKey.arrowRight,
      scope: HotKeyScope.inapp,
    );

    // 注册热键处理函数
    hotKeyManager.register(
      leftArrowHotKey.value!,
      keyDownHandler: (hotKey) {
        // 在图片编辑器路由中不处理方向键热键
        if (Get.currentRoute == "/image_editor") {
          return;
        }
        previousPage();
      },
    );

    hotKeyManager.register(
      rightArrowHotKey.value!,
      keyDownHandler: (hotKey) {
        // 在图片编辑器路由中不处理方向键热键
        if (Get.currentRoute == "/image_editor") {
          return;
        }
        nextPage();
      },
    );
  }

  // Process OCR for all images
  Future<void> _processAllOCR() async {
    if (!autoOCR.value || imageCards.isEmpty) return;

    isProcessing.value = true;
    final tempDir = await getTemporaryDirectory();
    final List<String> filePaths = [];
    final List<File> tempFiles = [];

    try {
      // Step 1: Save all images to temporary files with proper PNG formatting
      for (int i = 0; i < imageCards.length; i++) {
        try {
          // Convert to ui.Image for proper formatting
          final codec = await ui.instantiateImageCodec(imageCards[i].imageData);
          final frame = await codec.getNextFrame();
          final image = frame.image;

          // Convert back to PNG bytes
          final pngBytes =
              await image.toByteData(format: ui.ImageByteFormat.png);
          if (pngBytes == null) {
            logger.e("Failed to convert image $i to PNG format");
            continue;
          }

          final fileName =
              'ocr_image_${DateTime.now().millisecondsSinceEpoch}_$i.png';
          final tempFilePath = p.join(tempDir.path, fileName);
          final tempFile = File(tempFilePath);
          await tempFile.writeAsBytes(pngBytes.buffer.asUint8List());

          filePaths.add(tempFilePath);
          tempFiles.add(tempFile);
        } catch (e) {
          logger.e("Error preparing image $i for OCR: $e");
        }
      }

      if (filePaths.isEmpty) {
        logger.e("Failed to prepare any images for OCR");
        return;
      }

      // Step 2: Send batch OCR request for all images
      final data = {
        "file_paths": filePaths,
        "provider": "paddle-ocr",
        "model_name": "",
        "api_key": "",
        "base_url": "",
        "system_prompt": "",
        "merge_output": false,
        "response_format": "json",
        "protocol_type": "openai",
        "show_progress": false // Don't show progress dialog for automatic OCR
      };

      // Call OCR service
      final resp =
          await Get.find<MessageController>().request(data, 'anki/ocr');

      if (resp.status == "success") {
        try {
          final parsedTexts = OcrParser.parse(resp.data);
          logger.i("parsedTexts: $parsedTexts");
          // 将解析后的结果分配给对应的卡片
          for (int i = 0;
              i < parsedTexts.length && i < imageCards.length;
              i++) {
            try {
              // 直接使用解析后的文本
              imageCards[i].ocrText = parsedTexts[i];
            } catch (e) {
              logger.e("Error assigning OCR text for image $i: $e");
            }
          }

          imageCards.refresh();
          logger.i("Auto OCR completed for all images");
        } catch (e) {
          logger.e("Error parsing batch OCR results: $e");
        }
      } else {
        logger.e("Batch OCR failed: ${resp.message}");
      }
    } catch (e) {
      logger.e("OCR batch processing error: $e");
    } finally {
      // Clean up temporary files
      for (var file in tempFiles) {
        try {
          if (file.existsSync()) {
            await file.delete();
          }
        } catch (e) {
          // Ignore errors during cleanup
        }
      }
      isProcessing.value = false;
    }
  }
}
